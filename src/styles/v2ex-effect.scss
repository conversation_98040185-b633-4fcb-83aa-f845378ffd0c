@use './share';

body {
  position: relative;

  &.v2p-modal-open {
    overflow: hidden;
  }

  .button {
    &.v2p-prev-btn,
    &.v2p-next-btn {
      padding: 0 15px;
    }
  }
}

.v2p-hover-btn {
  @include share.hover-button;
}

.v2p-hover-btn-disabled {
  pointer-events: none;
  opacity: 0.8;
}

.v2p-icon-heart {
  display: inline-flex;
  width: 16px;
  height: 16px;
  color: var(--v2p-color-heart);

  svg {
    fill: var(--v2p-color-heart-fill);
  }
}

#Main {
  .cell[id^='r'] {
    .v2p-auto-hide {
      overflow: hidden;
      display: inline-flex;
      width: 0;
    }
  }


}

#Main,
#Rightbar {
  .cell {
    &:hover {
      .v2p-topic-ignore-btn {
        visibility: visible;
      }
    }
  }
}

#Rightbar {
  .v2p-info-row {
    display: block;
    font-size: 12px;
    color: var(--v2p-color-accent-500);
    text-align: center;

    &:hover {
      text-decoration: none;
      background-color: var(--v2p-color-accent-50);
    }
  }



  @at-root {
    .v2p-tool-box {
      position: sticky;
      z-index: var(--zidx-tools-card);
      top: var(--v2p-layout-row-gap);

      .v2p-tools {
        display: grid;
        grid-auto-rows: auto;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px 15px;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: var(--v2p-color-font-secondary);
      }
    }

    .v2p-tool {
      display: inline-flex;
      gap: 0 5px;
      align-items: center;
      padding: 3px 0;

      &:hover {
        color: var(--v2p-color-button-foreground-hover);
      }

      .v2p-tool-icon {
        width: 16px;
        height: 16px;
      }
    }
  }
}



.v2p-topic-ignore-btn {
  cursor: pointer;
  margin-left: 8px;
  visibility: hidden;
}





.v2p-dot {
  margin: 0 8px;
  font-size: 15px;
  font-weight: 800;
}

.v2p-paging {
  background: none !important;

  &.cell {
    border-bottom: none;
  }
}

.v2p-modal-mask {
  position: fixed;
  z-index: var(--zidx-modal-mask);
  inset: 0;
  overflow: hidden;
  overflow-y: auto;
  padding: min(2vh, 60px);
  background-color: var(--v2p-color-mask);
}

.v2p-popup {
  @include share.popup;

  position: absolute;
  z-index: var(--zidx-popup);
  top: 0;
  left: 0;
}

.v2p-popup-content {
  overflow-y: auto;
  width: max-content;
}

.v2p-toast {
  position: fixed;
  z-index: var(--zidx-toast);
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 15px;
  font-size: 14px;
  color: var(--v2p-color-background);
  background: var(--v2p-color-foreground);
  border-radius: 8px;
  box-shadow: var(--v2p-toast-shadow);
}

.v2p-modal-main {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 800px;
  height: 100%;
  margin: 0 auto;
  background-color: var(--v2p-color-bg-content);
  border-radius: var(--box-border-radius);

  @at-root {
    .v2p-modal-header {
      display: flex;
      gap: 0 20px;
      align-items: center;
      padding: 15px 20px 20px;
      background-color: var(--v2p-color-bg-content);
      border-bottom: 1px solid var(--box-border-color);
    }

    .v2p-modal-title {
      overflow: hidden;
      padding: 2px 0;
      font-size: 16px;
      font-weight: bold;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .v2p-modal-actions {
      display: flex;
      gap: 0 10px;
      align-items: center;
      margin-left: auto;
    }

    .v2p-modal-content {
      position: relative;
      overflow-y: auto;
      overscroll-behavior-y: contain; // 防止滚动穿透。
      flex: 1;
      outline: none;
    }

    .v2p-modal-comments {
      position: absolute;
      inset: 0;
      overflow-y: auto;
      padding: 0 20px;
      visibility: hidden;

      &.v2p-tab-content-active {
        z-index: 20;
        visibility: visible;
      }
    }

    .v2p-modal-comment-tabs {
      display: flex;
      gap: 4px;
      align-items: center;
      padding: 4px 5px;
      font-size: 14px;
      font-weight: normal;
      background-color: var(--button-background-color);
      border-radius: 4px;

      > [data-tab-key] {
        cursor: pointer;
        padding: 4px 5px;
        border-radius: 4px;

        &:hover {
          background-color: var(--v2p-color-button-background-hover);
        }

        &.v2p-tab-active {
          color: var(--v2p-color-foreground);
          background-color: var(--v2p-color-accent-100);
        }
      }
    }
  }
}

.v2p-no-pat {
  padding: 30px 20px;
  font-size: 15px;
  text-align: center;

  .v2p-no-pat-title {
    font-size: 16px;
    font-weight: bold;
  }

  .v2p-no-pat-desc {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }

  .v2p-no-pat-block {
    display: inline-flex;
    align-items: center;
    margin: 0 5px;
    padding: 2px 10px;
    background-color: var(--v2p-color-bg-block);
    border-radius: 2px;
  }

  .v2p-no-pat-steps {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    max-width: 800px;
    margin-top: 20px;
    padding: 20px;
    background-color: var(--v2p-color-bg-block);
    border-radius: 10px;
  }

  .v2p-no-pat-step {
    flex: 1;
  }

  .v2p-no-pat-img {
    width: 100%;
    border-radius: 8px;
    box-shadow: var(--v2p-widget-shadow);
  }

  .v2p-icon-logo {
    width: 15px;
    height: 15px;
  }
}

.v2p-likes-box {
  user-select: none;
  position: relative;
  top: 3px;
  display: inline-flex;
  column-gap: 5px;
  align-items: center;

  &.v2p-thanked {
    font-weight: bold;
    color: var(--v2p-color-heart);
    opacity: 0.8;

    .v2p-icon-heart {
      svg {
        fill: var(--v2p-color-heart);
      }
    }
  }
}

@supports not selector(:has(*)) {
  #Main .cell[id^='r'] > table:hover {
    .v2p-controls {
      opacity: 1;
    }
  }
}

@supports selector(:has(*)) {
  #Main .cell[id^='r']:not(:has(.cell:hover)) > table:hover {
    .v2p-auto-hide {
      width: auto;
    }

    .v2p-controls {
      opacity: 1;
    }
  }
}

.v2p-controls {
  display: inline-flex;
  column-gap: 15px;
  align-items: center;
  margin-right: 15px;
  font-size: 12px;
  opacity: 0;

  > a {
    text-decoration: none;
  }

  @at-root {
    .v2p-control {
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      padding: 4px 0;
      color: var(--v2p-color-font-tertiary);

      &::after {
        @include share.tooltip;

        position: absolute;
        top: -8px;
        transform: translateY(-100%);
        opacity: 0;
      }

      &:hover {
        color: var(--v2p-color-font-secondary);
      }

      &.v2p-thanked {
        cursor: default;
        color: var(--v2p-color-heart);
      }

      &:hover::after {
        opacity: 1;
      }

      @at-root {
        &.v2p-control-hide::after {
          content: '隐藏回复';
        }

        &.v2p-control-thank {
          &::after {
            content: '感谢回复';
          }

          &.v2p-thanked::after {
            content: '已感谢';
          }
        }

        &.v2p-control-reply::after {
          content: '回复';
        }
      }
    }
  }
}

.topic_buttons .v2p-tb.v2p-hover-btn {
  color: var(--v2p-color-font-secondary);

  &::after {
    display: none;
  }

  &:hover {
    color: currentColor;
  }
}

.v2p-tb-icon {
  width: 15px;
  height: 15px;
}

.v2p-emoji-container {
  overflow-y: auto;
  max-height: 285px;
  padding: 15px 18px;
  color: var(--v2p-color-font-secondary);
}

.v2p-member-card {
  max-width: 300px;
  max-height: 285px;
  padding: 12px;
  font-size: 13px;
  text-align: left;

  .v2p-info {
    display: flex;
    gap: 15px;
  }

  .v2p-info-right {
    padding: 2px 0;
  }

  .v2p-avatar-box {
    overflow: hidden;
    display: inline-block;
    width: 73px;
    height: 73px;
    background-color: var(--button-background-hover-color);
    border-radius: 5px;
  }

  .v2p-avatar {
    width: 100%;
    height: 100%;
  }

  .v2p-username {
    font-size: 16px;
    font-weight: bold;
  }

  .v2p-no {
    margin: 5px 0;
  }

  .v2p-no,
  .v2p-created-date {
    width: 160px;
    height: 16px;
  }

  .v2p-loading {
    background-color: var(--button-background-hover-color);
    border-radius: 4px;
  }

  .v2p-bio {
    @include share.line-clamp(3);

    margin-top: 10px;
  }
}

.v2p-member-card-actions {
  padding: 10px 0 0;
}

.v2p-reply-tags {
  cursor: pointer;
  display: inline-flex;
  margin: 0 0 2px;
  padding: 0 3px;
  font-size: 12px;
  background-color: var(--button-background-hover-color);
  border-radius: 3px;
}

.v2p-reply-tags-inline {
  overflow: hidden;
  max-width: 300px;
  margin: 0 5px 0 0;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.v2p-emoticons-box {
  font-size: 15px;
}

.v2p-emoji-group {
  & ~ & {
    margin-top: 10px;
  }
}

.v2p-emoji-title {
  margin: 0 0 10px;
  font-size: 14px;
  text-align: left;
}

.v2p-emoji-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 5px;
  font-size: 20px;
}

.v2p-emoji {
  cursor: pointer;
  height: 20px;
  padding: 3px;
  line-height: 20px;
  border-radius: 4px;

  &:hover {
    background-color: var(--box-background-hover-color);
  }

  > img {
    height: 100%;
  }
}

.v2p-decode {
  cursor: copy;
  position: relative;
  padding: 2px 4px;
  font-size: 13px;
  color: var(--v2p-color-orange-400);
  text-decoration: none;
  background-color: var(--v2p-color-orange-50);

  &::after {
    @include share.tooltip;

    content: attr(data-title);
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translate(-50%, -100%);
    opacity: 0;
  }

  &:hover {
    color: var(--v2p-color-orange-400);

    &::after {
      opacity: 1;
    }
  }
}

.v2p-reply-content {
  position: relative;

  .v2p-expand-btn.normal.button {
    position: absolute;
    z-index: var(--zidx-expand-btn);
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: 400;
  }

  &.v2p-collapsed {
    &::before {
      pointer-events: none;
      content: '';
      position: absolute;
      z-index: var(--zidx-expand-mask);
      right: 0;
      bottom: 0;
      left: 0;
      height: 130px;
      background: linear-gradient(to top, var(--bg-reply) 10px, transparent);
    }

    .reply_content {
      a,
      .embedded_video {
        pointer-events: none;
      }
    }

    .v2p-expand-btn.normal.button {
      bottom: 10px;
      transform: translateX(-50%);
    }
  }
}

.cell[id^='r'] .cell[id^='r'] {
  // 加深嵌套回复中「展开按钮」的背景色。
  .v2p-reply-content {
    .v2p-expand-btn.normal.button {
      color: var(--v2p-color-button-foreground);
      background: var(--v2p-color-button-background-hover);
      box-shadow: var(--button-hover-shadow);
    }
  }
}

.v2p-empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 20px;
  font-size: 14px;
  color: var(--v2p-color-font-secondary);

  .v2p-text-emoji {
    font-size: 20px;
  }
}

.v2p-topic-reply-ref {
  margin: 0 -10px 15px;
  padding: 5px 10px;
  font-size: 13px;
  color: var(--v2p-color-font-tertiary);
  background-color: var(--v2p-color-bg-block);
  border-radius: 5px;
}

.v2p-topic-reply-box {
  margin-top: 50px;
  padding: 30px 0;
  font-size: 14px;
  line-height: 1.55;
  color: var(--v2p-color-font-secondary);
  border-top: 1px solid var(--v2p-color-divider);

  @at-root {
    .v2p-topic-reply {
      & ~ & {
        margin-top: 15px;
      }

      @at-root {
        .v2p-topic-reply-member {
          display: inline;
          font-weight: bold;
          color: var(--v2p-color-main-700);
        }

        .v2p-topic-reply-avatar {
          position: relative;
          top: 2px;
          overflow: hidden;
          width: 15px;
          height: 15px;
          margin-right: 5px;
          object-fit: cover;
          background-color: var(--v2p-color-bg-avatar);
          border-radius: 2px;
        }

        .v2p-topic-reply-content {
          display: inline;
        }

        .v2p-topic-reply-tip {
          margin-top: 20px;
          font-size: 13px;
          color: var(--v2p-color-font-quaternary);
          text-align: center;
        }
      }
    }

    .v2p-reply-wrap {
      @include share.input(140px);

      #reply_content {
        background-color: transparent;
        border: none;

        &::placeholder {
          font-size: 14px;
          color: var(--v2p-color-font-tertiary);
        }

        &:focus {
          background-color: var(--v2p-color-bg-content);
          outline: none;
        }
      }
    }

    .v2p-reply-upload-bar {
      cursor: pointer;
      padding: 6px 10px;
      font-size: 12px;
      color: var(--v2p-color-font-tertiary);
      background-color: var(--v2p-color-bg-input);
      border-top: 1px dashed var(--v2p-color-border-darker);
    }

    .v2p-reply-upload-bar-disabled {
      pointer-events: none;
    }
  }
}

#Bottom {
  @at-root {
    .v2p-footer {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 35px 10px 20px;
      font-size: 12px;
      color: var(--v2p-color-font-tertiary);
      border-top: 1px solid var(--v2p-color-divider);

      a {
        &:hover {
          text-decoration: none;
        }
      }
    }

    .v2p-footer-logo {
      --logo-size: 16px;

      position: absolute;
      top: calc(-1 * (var(--logo-size) + 5px) / 2);
      left: 50%;
      transform: translateX(-50%);
      display: inline-flex;
      box-sizing: border-box;
      padding: 3px 25px;
      background-color: var(--v2p-color-bg-footer);

      svg {
        width: var(--logo-size);
      }
    }

    .v2p-footer-text {
      display: inline-flex;
      align-items: center;
      justify-content: flex-start;
      width: 240px;
      color: var(--v2p-color-font-secondary);
    }

    .v2p-footer-links {
      display: inline-flex;
      gap: 0 8px;
      align-items: center;
    }

    .v2p-footer-link {
      padding: 4px 5px;
      color: currentColor;
    }

    .v2p-footer-brand {
      display: inline-flex;
      gap: 0 15px;
      align-items: center;
      justify-content: flex-end;
      width: 240px;

      .v2p-github-ref {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        padding: 2px 0;
      }
    }
  }
}

.v2p-color-mode-toggle {
  width: 22px;
  height: 22px;
  opacity: 0.8;

  &:hover {
    opacity: 1;
  }
}

.v2p-reply-tabs {
  display: flex;
  gap: 0 6px;
  align-items: center;
  font-size: 14px;

  .v2p-reply-tab {
    cursor: pointer;
    padding: 2px 3px;

    &.active {
      text-decoration: underline;
      text-decoration-color: var(--v2p-color-font-tertiary);
      text-decoration-thickness: 2px;
      text-underline-offset: 4px;
    }
  }
}

.v2p-select-dropdown {
  padding: 5px;
  font-size: 12px;
  border-radius: 5px;

  @at-root {
    .v2p-select-item {
      cursor: pointer;
      padding: 5px 10px;
      white-space: nowrap;
      border-radius: 4px;

      &:hover {
        background-color: var(--v2p-color-button-background-hover);
      }

      &.v2p-select-item-active {
        background-color: var(--v2p-color-accent-50);
      }
    }
  }
}



.v2p-indent {
  .v2p-member-ref {
    display: none; // 只有在嵌套回复中才能隐藏 @ 用户。
  }
}

.v2p-member-ref {
  + br {
    display: none;

    + b {
      display: none;
    }
  }

  &.v2p-member-ref-show {
    display: inline;
  }
}

.v2p-layout-toggle {
  display: inline-block;
  width: 18px;
  height: 18px;
  padding: 4px 2px;
  color: var(--v2p-color-font-tertiary);
}

.v2p-content-layout.v2p-content-layout {
  max-width: 2000px;

  .v2p-horizontal-layout {
    display: flex;
    flex-wrap: wrap;
    gap: var(--v2p-layout-column-gap);

    @at-root {
      .v2p-left-side {
        flex: 1;

        > .box {
          position: sticky;
          top: var(--v2p-layout-row-gap);
          display: flex;
          flex-direction: column;
          max-height: calc(100vh - 2 * var(--v2p-layout-row-gap));

          > .header {
            flex-shrink: 0;
          }
        }

        .v2p-left-side-content {
          overflow: auto;
          flex: 1;
          border-bottom: 1px solid var(--box-border-color);
        }
      }

      .v2p-right-side {
        flex: 1;
      }
    }
  }
}

.v2p-register-days {
  display: inline-flex;
  align-items: center;
  margin-left: 2px;
  padding: 0 2px;
  color: var(--v2p-color-orange-400);
  background-color: var(--v2p-color-orange-100);
  border-radius: 2px;
}

.v2p-topics-hot-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  color: currentColor;

  .v2p-icon-loading {
    width: 40px;
  }
}

.v2p-topics-hot-header {
  display: flex;
  align-items: center;
}

.v2p-topics-hot-picker {
  cursor: pointer;
  display: inline-flex;
  gap: 4px;
  align-items: center;
  margin-left: auto;
  padding: 1px 6px;
  font-size: 13px;
  background-color: var(--v2p-color-button-background);
  border-radius: 4px;

  &:hover {
    background-color: var(--v2p-color-button-background-hover);
  }

  @at-root {
    .v2p-topics-hot-icon {
      position: relative;
      top: -2px;
      width: 1em;
      height: 1em;
    }
  }
}

// MARK: 用户标签。
.v2p-tag-block {
  margin-bottom: 10px;
}

// MARK: 隐藏用户信息。
.v2p-hide-account {
  @supports (filter: blur(6px)) {
    opacity: 0.5;
    filter: blur(6px);
  }

  @supports not (filter: blur(6px)) {
    opacity: 0;
  }
}
